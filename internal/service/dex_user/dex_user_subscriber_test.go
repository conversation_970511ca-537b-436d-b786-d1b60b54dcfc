package dex_user

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/nats-io/nats.go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

// MockNATSSubscriber is a mock implementation of natsClient.Subscriber
type MockNATSSubscriber struct {
	mock.Mock
}

func (m *MockNATSSubscriber) Subscribe(subject string, handler nats.MsgHandler) (*nats.Subscription, error) {
	args := m.Called(subject, handler)
	return args.Get(0).(*nats.Subscription), args.Error(1)
}

func (m *MockNATSSubscriber) SubscribeJS(subject string, handler nats.MsgHandler, opts ...nats.SubOpt) (*nats.Subscription, error) {
	args := m.Called(subject, handler, opts)
	return args.Get(0).(*nats.Subscription), args.Error(1)
}

func (m *MockNATSSubscriber) AddStream(cfg *nats.StreamConfig) (*nats.StreamInfo, error) {
	args := m.Called(cfg)
	return args.Get(0).(*nats.StreamInfo), args.Error(1)
}

func (m *MockNATSSubscriber) ConsumerInfo(stream, consumer string) (*nats.ConsumerInfo, error) {
	args := m.Called(stream, consumer)
	return args.Get(0).(*nats.ConsumerInfo), args.Error(1)
}

func (m *MockNATSSubscriber) DeleteConsumer(stream, consumer string) error {
	args := m.Called(stream, consumer)
	return args.Error(0)
}

func TestDexUserSubscriberService_parseChainType(t *testing.T) {
	service := &DexUserSubscriberService{}

	tests := []struct {
		name     string
		chain    string
		expected model.ChainType
		hasError bool
	}{
		{
			name:     "EVM chain",
			chain:    "EVM",
			expected: model.ChainEvm,
			hasError: false,
		},
		{
			name:     "SOLANA chain",
			chain:    "SOLANA",
			expected: model.ChainSolana,
			hasError: false,
		},
		{
			name:     "TRON chain",
			chain:    "TRON",
			expected: model.ChainTron,
			hasError: false,
		},
		{
			name:     "ARB chain",
			chain:    "ARB",
			expected: model.ChainArb,
			hasError: false,
		},
		{
			name:     "Unsupported chain",
			chain:    "UNKNOWN",
			expected: "",
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.parseChainType(tt.chain)

			if tt.hasError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "unsupported chain type")
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

// Simplified test without database dependencies for now
func TestDexUserSubscriberService_processUserCreation_InvalidUserID(t *testing.T) {
	// Create test service with nil dependencies for this simple test
	service := &DexUserSubscriberService{}

	ctx := context.Background()

	userEvent := &natsClient.DexUserEvent{
		UserID:  "invalid-uuid",
		Wallets: []natsClient.DexUserWallet{},
	}

	err := service.processUserCreation(ctx, userEvent)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid user ID format")
}

func TestDexUserEvent_JSONUnmarshaling(t *testing.T) {
	// Test JSON data similar to the example provided
	jsonData := `{
		"userId": "0199085a-9ddf-7368-85b2-ce5ae8b37b5f",
		"wallets": [
			{
				"id": "0199085a-a69a-7d17-b388-0bc135604922",
				"chain": "EVM",
				"walletAddress": "******************************************",
				"walletAccountId": "7d3d71d3-8da7-412c-9c8f-168cb0ad44e3",
				"walletId": "395448f9-1249-50ea-831b-490eed4774c0",
				"hdPath": "m/44'/60'/0'/0/0",
				"name": "Main Account",
				"createdAt": "2025-09-02T02:56:22.682Z"
			},
			{
				"id": "0199085a-a6ae-7db0-afef-270bec54251c",
				"chain": "SOLANA",
				"walletAddress": "6QFvVCn1rSbkJadRrwXxYGh3efXNpMdSNrBQSesEJ4DB",
				"walletAccountId": "a59a0de3-47e8-486b-bd3b-a4770ab93587",
				"walletId": "395448f9-1249-50ea-831b-490eed4774c0",
				"hdPath": "m/44'/501'/0'/0'",
				"name": "Main Account",
				"createdAt": "2025-09-02T02:56:22.702Z"
			}
		]
	}`

	var userEvent natsClient.DexUserEvent
	err := json.Unmarshal([]byte(jsonData), &userEvent)
	assert.NoError(t, err)

	assert.Equal(t, "0199085a-9ddf-7368-85b2-ce5ae8b37b5f", userEvent.UserID)
	assert.Len(t, userEvent.Wallets, 2)

	// Test first wallet
	wallet1 := userEvent.Wallets[0]
	assert.Equal(t, "0199085a-a69a-7d17-b388-0bc135604922", wallet1.ID)
	assert.Equal(t, "EVM", wallet1.Chain)
	assert.Equal(t, "******************************************", wallet1.WalletAddress)
	assert.Equal(t, "7d3d71d3-8da7-412c-9c8f-168cb0ad44e3", wallet1.WalletAccountID)
	assert.Equal(t, "395448f9-1249-50ea-831b-490eed4774c0", wallet1.WalletID)
	assert.Equal(t, "m/44'/60'/0'/0/0", wallet1.HDPath)
	assert.Equal(t, "Main Account", wallet1.Name)

	// Test second wallet
	wallet2 := userEvent.Wallets[1]
	assert.Equal(t, "0199085a-a6ae-7db0-afef-270bec54251c", wallet2.ID)
	assert.Equal(t, "SOLANA", wallet2.Chain)
	assert.Equal(t, "6QFvVCn1rSbkJadRrwXxYGh3efXNpMdSNrBQSesEJ4DB", wallet2.WalletAddress)
}
