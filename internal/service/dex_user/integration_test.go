package dex_user

import (
	"context"
	"testing"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/stretchr/testify/mock"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

func TestDexUserSubscriberService_Integration(t *testing.T) {
	// Setup test environment
	_, _, helper := test.SetupTest(t)
	defer test.TeardownTest()

	t.Run("Service can be created and initialized", func(t *testing.T) {
		// Create mock NATS client
		mockNATS := &MockNATSSubscriber{}

		// Mock AddStream to return success
		mockNATS.On("AddStream", mock.Anything).Return(&nats.StreamInfo{}, nil)

		// Create agent referral service
		agentReferralService := agent_referral.NewAgentReferralService()

		// Create dex user subscriber service
		service := NewDexUserSubscriberService(mockNATS, agentReferralService)

		helper.AssertNotNil(service)
		helper.AssertNotNil(service.natsClient)
		helper.AssertNotNil(service.agentReferralService)
		helper.AssertNotNil(service.subscriptions)
	})

	t.Run("Service can handle graceful shutdown", func(t *testing.T) {
		// Create mock NATS client
		mockNATS := &MockNATSSubscriber{}

		// Create agent referral service
		agentReferralService := agent_referral.NewAgentReferralService()

		// Create dex user subscriber service
		service := NewDexUserSubscriberService(mockNATS, agentReferralService)

		// Test Stop method
		service.Stop()

		// Should not panic or cause issues
		helper.AssertTrue(true)
	})

	t.Run("Service handles context cancellation", func(t *testing.T) {
		// Create context with timeout
		ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
		defer cancel()

		// Test that context cancellation is handled
		go func() {
			time.Sleep(50 * time.Millisecond)
			cancel()
		}()

		// Wait for context to be cancelled
		<-ctx.Done()

		// Should not panic
		helper.AssertTrue(true)
	})
}

func TestDexUserSubscriberService_ErrorHandling(t *testing.T) {
	_, _, helper := test.SetupTest(t)
	defer test.TeardownTest()

	t.Run("Handle nil NATS client gracefully", func(t *testing.T) {
		// Create agent referral service
		agentReferralService := agent_referral.NewAgentReferralService()

		// Create service with nil NATS client
		service := NewDexUserSubscriberService(nil, agentReferralService)

		helper.AssertNotNil(service)
		helper.AssertNil(service.natsClient)
	})

	t.Run("Handle nil agent referral service gracefully", func(t *testing.T) {
		// Create mock NATS client
		mockNATS := &MockNATSSubscriber{}

		// Create service with nil agent referral service
		service := NewDexUserSubscriberService(mockNATS, nil)

		helper.AssertNotNil(service)
		helper.AssertNil(service.agentReferralService)
	})
}
