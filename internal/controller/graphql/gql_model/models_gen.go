// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package gql_model

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
	"time"
)

type ActivityCashbackClaim struct {
	ID              string      `json:"id"`
	UserID          string      `json:"userId"`
	ClaimType       ClaimType   `json:"claimType"`
	TotalAmountUsd  float64     `json:"totalAmountUsd"`
	TotalAmountSol  float64     `json:"totalAmountSol"`
	TransactionHash *string     `json:"transactionHash,omitempty"`
	Status          ClaimStatus `json:"status"`
	ClaimedAt       time.Time   `json:"claimedAt"`
	ProcessedAt     *time.Time  `json:"processedAt,omitempty"`
	Metadata        *string     `json:"metadata,omitempty"`
	CreatedAt       time.Time   `json:"createdAt"`
	UpdatedAt       time.Time   `json:"updatedAt"`
}

type ActivityTask struct {
	ID                 string            `json:"id"`
	CategoryID         string            `json:"categoryId"`
	Name               string            `json:"name"`
	Description        *string           `json:"description,omitempty"`
	TaskType           TaskType          `json:"taskType"`
	Frequency          TaskFrequency     `json:"frequency"`
	TaskIdentifier     *TaskIdentifier   `json:"taskIdentifier,omitempty"`
	Points             int               `json:"points"`
	MaxCompletions     *int              `json:"maxCompletions,omitempty"`
	ResetPeriod        *string           `json:"resetPeriod,omitempty"`
	Conditions         *string           `json:"conditions,omitempty"`
	ActionTarget       *string           `json:"actionTarget,omitempty"`
	VerificationMethod *string           `json:"verificationMethod,omitempty"`
	ExternalLink       *string           `json:"externalLink,omitempty"`
	IsActive           bool              `json:"isActive"`
	StartDate          *time.Time        `json:"startDate,omitempty"`
	EndDate            *time.Time        `json:"endDate,omitempty"`
	SortOrder          int               `json:"sortOrder"`
	CreatedAt          time.Time         `json:"createdAt"`
	UpdatedAt          time.Time         `json:"updatedAt"`
	Category           *TaskCategory     `json:"category,omitempty"`
	UserProgress       *UserTaskProgress `json:"userProgress,omitempty"`
}

type AgentLevel struct {
	ID                      int     `json:"id"`
	Name                    string  `json:"name"`
	MemeVolumeThreshold     float64 `json:"memeVolumeThreshold"`
	ContractVolumeThreshold float64 `json:"contractVolumeThreshold"`
	MemeFeeRate             float64 `json:"memeFeeRate"`
	TakerFeeRate            float64 `json:"takerFeeRate"`
	MakerFeeRate            float64 `json:"makerFeeRate"`
	DirectCommissionRate    float64 `json:"directCommissionRate"`
	IndirectCommissionRate  float64 `json:"indirectCommissionRate"`
	ExtendedCommissionRate  float64 `json:"extendedCommissionRate"`
	MemeFeeRebate           float64 `json:"memeFeeRebate"`
}

type CashbackClaimResponse struct {
	Success   bool    `json:"success"`
	Message   string  `json:"message"`
	ClaimID   string  `json:"claimId"`
	AmountUsd float64 `json:"amountUsd"`
	AmountSol float64 `json:"amountSol"`
}

type ClaimActivityCashbackInput struct {
	ClaimActivityCashback string `json:"claimActivityCashback"`
}

type ClaimAgentReferralInput struct {
	ClaimAgentReferral string `json:"claimAgentReferral"`
}

type ClaimCashbackInput struct {
	AmountUsd float64 `json:"amountUsd"`
}

type ClaimResultResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type ClaimRewardResponse struct {
	ClaimActivityCashback string `json:"claimActivityCashback"`
	ClaimAgentReferral    string `json:"claimAgentReferral"`
}

type ClaimTaskRewardInput struct {
	TaskID string `json:"taskId"`
}

type CompleteTaskInput struct {
	TaskID           string  `json:"taskId"`
	VerificationData *string `json:"verificationData,omitempty"`
}

type CreateInfiniteAgentConfigInput struct {
	UserID          string     `json:"userID"`
	CommissionRateN float64    `json:"commissionRateN"`
	Status          StatusType `json:"status"`
}

type CreateInfiniteAgentConfigResponse struct {
	InfiniteAgentConfig *InfiniteAgentConfig `json:"infiniteAgentConfig,omitempty"`
	Success             bool                 `json:"success"`
	Message             string               `json:"message"`
}

type CreateInfiniteAgentReferralTreeResponse struct {
	Success             bool   `json:"success"`
	Message             string `json:"message"`
	ProcessedCount      int    `json:"processedCount"`
	ErrorCount          int    `json:"errorCount"`
	TotalInfiniteAgents int    `json:"totalInfiniteAgents"`
	SnapshotDate        string `json:"snapshotDate"`
}

type CreateReferralTreeSnapshotResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type CreateTaskInput struct {
	CategoryID         string          `json:"categoryId"`
	Name               string          `json:"name"`
	Description        *string         `json:"description,omitempty"`
	TaskType           TaskType        `json:"taskType"`
	Frequency          TaskFrequency   `json:"frequency"`
	TaskIdentifier     *TaskIdentifier `json:"taskIdentifier,omitempty"`
	Points             int             `json:"points"`
	MaxCompletions     *int            `json:"maxCompletions,omitempty"`
	ResetPeriod        *string         `json:"resetPeriod,omitempty"`
	Conditions         *string         `json:"conditions,omitempty"`
	ActionTarget       *string         `json:"actionTarget,omitempty"`
	VerificationMethod *string         `json:"verificationMethod,omitempty"`
	ExternalLink       *string         `json:"externalLink,omitempty"`
	StartDate          *time.Time      `json:"startDate,omitempty"`
	EndDate            *time.Time      `json:"endDate,omitempty"`
	SortOrder          *int            `json:"sortOrder,omitempty"`
}

type CreateTierBenefitInput struct {
	TierLevel           int     `json:"tierLevel"`
	TierName            string  `json:"tierName"`
	MinPoints           int     `json:"minPoints"`
	CashbackPercentage  float64 `json:"cashbackPercentage"`
	BenefitsDescription *string `json:"benefitsDescription,omitempty"`
	TierColor           *string `json:"tierColor,omitempty"`
	TierIcon            *string `json:"tierIcon,omitempty"`
}

type CreateUserInput struct {
	Email          string  `json:"email"`
	InvitationCode *string `json:"invitationCode,omitempty"`
	ReferrerCode   *string `json:"referrerCode,omitempty"`
}

type CreateUserInvitationCodeInput struct {
	InvitationCode  string     `json:"invitationCode"`
	Email           *string    `json:"email,omitempty"`
	Chain           *string    `json:"chain,omitempty"`
	Name            *string    `json:"name,omitempty"`
	WalletAddress   *string    `json:"walletAddress,omitempty"`
	WalletID        *string    `json:"walletId,omitempty"`
	WalletAccountID *string    `json:"walletAccountId,omitempty"`
	WalletType      WalletType `json:"walletType"`
}

type CreateUserResponse struct {
	User    *User  `json:"user"`
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type CreateUserWithReferralInput struct {
	InvitationCode string `json:"invitationCode"`
}

type DataOverview struct {
	RebateAmount      *DataOverviewCategory `json:"rebateAmount"`
	TransactionVolume *DataOverviewCategory `json:"transactionVolume"`
	InvitationCount   *DataOverviewCategory `json:"invitationCount"`
	Timestamp         time.Time             `json:"timestamp"`
	Period            string                `json:"period"`
}

type DataOverviewCategory struct {
	All      float64 `json:"all"`
	Meme     float64 `json:"meme"`
	Contract float64 `json:"contract"`
}

type DataOverviewInput struct {
	TimeRange DataOverviewTimeRange `json:"timeRange"`
}

type DataOverviewResponse struct {
	Data    []*DataOverview `json:"data"`
	Success bool            `json:"success"`
	Message *string         `json:"message,omitempty"`
}

type DataOverviewSummary struct {
	TotalRebateAmount        *DataOverviewCategory `json:"totalRebateAmount"`
	TotalTransactionVolume   *DataOverviewCategory `json:"totalTransactionVolume"`
	TotalInvitationCount     *DataOverviewCategory `json:"totalInvitationCount"`
	PeakRebateAmount         *DataOverviewCategory `json:"peakRebateAmount"`
	PeakTransactionVolume    *DataOverviewCategory `json:"peakTransactionVolume"`
	PeakInvitationCount      *DataOverviewCategory `json:"peakInvitationCount"`
	AverageRebateAmount      *DataOverviewCategory `json:"averageRebateAmount"`
	AverageTransactionVolume *DataOverviewCategory `json:"averageTransactionVolume"`
	AverageInvitationCount   *DataOverviewCategory `json:"averageInvitationCount"`
}

type DataOverviewWithSummary struct {
	Data    []*DataOverview      `json:"data"`
	Summary *DataOverviewSummary `json:"summary"`
	Success bool                 `json:"success"`
	Message *string              `json:"message,omitempty"`
}

type InfiniteAgentConfig struct {
	ID              string    `json:"id"`
	UserID          string    `json:"userID"`
	CommissionRateN float64   `json:"commissionRateN"`
	Status          string    `json:"Status"`
	CreatedAt       time.Time `json:"createdAt"`
	UpdatedAt       time.Time `json:"updatedAt"`
	User            *User     `json:"user,omitempty"`
}

type InfiniteAgentConfigResponse struct {
	InfiniteAgentConfig *InfiniteAgentConfig `json:"infiniteAgentConfig,omitempty"`
	Success             bool                 `json:"success"`
	Message             string               `json:"message"`
}

type InfiniteAgentConfigsResponse struct {
	InfiniteAgentConfigs []*InfiniteAgentConfig `json:"infiniteAgentConfigs"`
	Success              bool                   `json:"success"`
	Message              string                 `json:"message"`
}

type InfiniteAgentReferralTree struct {
	ID                    string                   `json:"id"`
	CreatedAt             time.Time                `json:"createdAt"`
	InfiniteAgentUserID   string                   `json:"infiniteAgentUserId"`
	CommissionRateN       float64                  `json:"commissionRateN"`
	RootUserID            string                   `json:"rootUserId"`
	SnapshotDate          time.Time                `json:"snapshotDate"`
	TotalNodes            int                      `json:"totalNodes"`
	MaxDepth              int                      `json:"maxDepth"`
	DirectCount           int                      `json:"directCount"`
	ActiveUsers           int                      `json:"activeUsers"`
	TradingUsers          int                      `json:"tradingUsers"`
	TotalCommissionEarned float64                  `json:"totalCommissionEarned"`
	TotalVolumeUsd        float64                  `json:"totalVolumeUsd"`
	Status                string                   `json:"status"`
	Description           *string                  `json:"description,omitempty"`
	InfiniteAgentUser     *User                    `json:"infiniteAgentUser"`
	InfiniteAgentConfig   *InfiniteAgentConfig     `json:"infiniteAgentConfig"`
	RootUser              *User                    `json:"rootUser"`
	TreeNodes             []*InfiniteAgentTreeNode `json:"treeNodes"`
}

type InfiniteAgentReferralTreeResponse struct {
	InfiniteAgentReferralTree *InfiniteAgentReferralTree `json:"infiniteAgentReferralTree,omitempty"`
	InfiniteAgentTreeNodes    []*InfiniteAgentTreeNode   `json:"infiniteAgentTreeNodes"`
	Success                   bool                       `json:"success"`
	Message                   string                     `json:"message"`
}

type InfiniteAgentReferralTreesResponse struct {
	InfiniteAgentReferralTrees []*InfiniteAgentReferralTree `json:"infiniteAgentReferralTrees"`
	Success                    bool                         `json:"success"`
	Message                    string                       `json:"message"`
}

type InfiniteAgentTreeNode struct {
	ID               string                     `json:"id"`
	CreatedAt        time.Time                  `json:"createdAt"`
	TreeID           string                     `json:"treeID"`
	UserID           string                     `json:"userID"`
	ParentUserID     *string                    `json:"parentUserID,omitempty"`
	ReferrerID       *string                    `json:"referrerID,omitempty"`
	Depth            int                        `json:"depth"`
	Level            int                        `json:"level"`
	Position         int                        `json:"position"`
	IsActive         bool                       `json:"isActive"`
	IsTrading        bool                       `json:"isTrading"`
	AgentLevelID     int                        `json:"agentLevelID"`
	CommissionEarned float64                    `json:"commissionEarned"`
	VolumeUsd        float64                    `json:"volumeUsd"`
	FeeVolumeUsd     float64                    `json:"feeVolumeUsd"`
	Tree             *InfiniteAgentReferralTree `json:"tree"`
	User             *User                      `json:"user"`
	ParentUser       *User                      `json:"parentUser,omitempty"`
	Referrer         *User                      `json:"referrer,omitempty"`
	AgentLevel       *AgentLevel                `json:"agentLevel"`
}

type InvitationListItem struct {
	UserAddress           string    `json:"userAddress"`
	InvitationTime        time.Time `json:"invitationTime"`
	TransactionType       string    `json:"transactionType"`
	TransactionAmount     float64   `json:"transactionAmount"`
	AccumulatedCommission float64   `json:"accumulatedCommission"`
	Date                  string    `json:"date"`
}

type InvitationListRequest struct {
	TransactionType TransactionType `json:"transactionType"`
	Page            int             `json:"page"`
	PageSize        int             `json:"pageSize"`
}

type InvitationListResponse struct {
	Data     []*InvitationListItem `json:"data"`
	Total    int                   `json:"total"`
	Page     int                   `json:"page"`
	PageSize int                   `json:"pageSize"`
	Success  bool                  `json:"success"`
	Message  *string               `json:"message,omitempty"`
}

type InvitationRecord struct {
	Address           string  `json:"address"`
	TransactionVolume float64 `json:"transactionVolume"`
	InvitedWithdrawal float64 `json:"invitedWithdrawal"`
	Date              string  `json:"date"`
}

type InvitationRecordRequest struct {
	Page     int `json:"page"`
	PageSize int `json:"pageSize"`
}

type InvitationRecordResponse struct {
	Success  bool                `json:"success"`
	Message  string              `json:"message"`
	Data     []*InvitationRecord `json:"data"`
	Total    int                 `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"pageSize"`
}

type InvitationSummary struct {
	InvitedUserCount int `json:"invitedUserCount"`
	TradingUserCount int `json:"tradingUserCount"`
}

type InvitationSummaryResponse struct {
	Data    *InvitationSummary `json:"data,omitempty"`
	Success bool               `json:"success"`
	Message *string            `json:"message,omitempty"`
}

type Mutation struct {
}

type Query struct {
}

type Referral struct {
	ID         int     `json:"id"`
	UserID     string  `json:"userId"`
	ReferrerID *string `json:"referrerId,omitempty"`
	Depth      int     `json:"depth"`
	CreatedAt  string  `json:"createdAt"`
	User       *User   `json:"user"`
	Referrer   *User   `json:"referrer,omitempty"`
}

type ReferralSnapshot struct {
	UserID                  string  `json:"userId"`
	DirectCount             int     `json:"directCount"`
	TotalDownlineCount      int     `json:"totalDownlineCount"`
	TotalVolumeUsd          float64 `json:"totalVolumeUsd"`
	TotalRewardsDistributed float64 `json:"totalRewardsDistributed"`
	L1UplineID              *string `json:"l1UplineId,omitempty"`
	L2UplineID              *string `json:"l2UplineId,omitempty"`
	L3UplineID              *string `json:"l3UplineId,omitempty"`
	User                    *User   `json:"user"`
	L1Upline                *User   `json:"l1Upline,omitempty"`
	L2Upline                *User   `json:"l2Upline,omitempty"`
	L3Upline                *User   `json:"l3Upline,omitempty"`
}

type ReferralTreeNode struct {
	ID             string                `json:"id"`
	CreatedAt      time.Time             `json:"createdAt"`
	TreeSnapshotID string                `json:"treeSnapshotID"`
	UserID         string                `json:"userID"`
	ParentUserID   *string               `json:"parentUserID,omitempty"`
	ReferrerID     *string               `json:"referrerID,omitempty"`
	Depth          int                   `json:"depth"`
	Level          int                   `json:"level"`
	Position       int                   `json:"position"`
	IsActive       bool                  `json:"isActive"`
	IsTrading      bool                  `json:"isTrading"`
	AgentLevelID   int                   `json:"agentLevelID"`
	TreeSnapshot   *ReferralTreeSnapshot `json:"treeSnapshot"`
	User           *User                 `json:"user"`
	ParentUser     *User                 `json:"parentUser,omitempty"`
	Referrer       *User                 `json:"referrer,omitempty"`
	AgentLevel     *AgentLevel           `json:"agentLevel"`
}

type ReferralTreeSnapshot struct {
	ID                  string               `json:"id"`
	CreatedAt           time.Time            `json:"createdAt"`
	RootUserID          string               `json:"rootUserId"`
	SnapshotDate        time.Time            `json:"snapshotDate"`
	TotalNodes          int                  `json:"totalNodes"`
	MaxDepth            int                  `json:"maxDepth"`
	DirectCount         int                  `json:"directCount"`
	ActiveUsers         int                  `json:"activeUsers"`
	TradingUsers        int                  `json:"tradingUsers"`
	InfiniteAgentUserID *string              `json:"infiniteAgentUserId,omitempty"`
	HasInfiniteAgent    bool                 `json:"hasInfiniteAgent"`
	Description         *string              `json:"description,omitempty"`
	IsValid             bool                 `json:"isValid"`
	RootUser            *User                `json:"rootUser"`
	InfiniteAgentUser   *User                `json:"infiniteAgentUser,omitempty"`
	InfiniteAgentConfig *InfiniteAgentConfig `json:"infiniteAgentConfig,omitempty"`
}

type ReferralTreeSnapshotResponse struct {
	ReferralTreeSnapshot *ReferralTreeSnapshot `json:"referralTreeSnapshot,omitempty"`
	ReferralTreeNodes    []*ReferralTreeNode   `json:"referralTreeNodes"`
	Success              bool                  `json:"success"`
	Message              string                `json:"message"`
}

type ReferralTreeSnapshotsResponse struct {
	ReferralTreeSnapshots []*ReferralTreeSnapshot `json:"referralTreeSnapshots"`
	Success               bool                    `json:"success"`
	Message               string                  `json:"message"`
}

type TaskCategory struct {
	ID          string          `json:"id"`
	Name        string          `json:"name"`
	DisplayName string          `json:"displayName"`
	Description *string         `json:"description,omitempty"`
	Icon        *string         `json:"icon,omitempty"`
	SortOrder   int             `json:"sortOrder"`
	IsActive    bool            `json:"isActive"`
	CreatedAt   time.Time       `json:"createdAt"`
	UpdatedAt   time.Time       `json:"updatedAt"`
	Tasks       []*ActivityTask `json:"tasks,omitempty"`
}

type TaskCategoryWithTasks struct {
	Category *TaskCategory       `json:"category"`
	Tasks    []*TaskWithProgress `json:"tasks"`
}

type TaskCenter struct {
	Categories        []*TaskCategoryWithTasks `json:"categories"`
	UserProgress      []*UserTaskProgress      `json:"userProgress"`
	CompletedToday    int                      `json:"completedToday"`
	PointsEarnedToday int                      `json:"pointsEarnedToday"`
	StreakTasks       []*UserTaskProgress      `json:"streakTasks"`
}

type TaskCenterResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    *TaskCenter `json:"data,omitempty"`
}

type TaskClaimResponse struct {
	Success       bool   `json:"success"`
	Message       string `json:"message"`
	PointsClaimed int    `json:"pointsClaimed"`
}

type TaskCompletionHistory struct {
	ID               string        `json:"id"`
	UserID           string        `json:"userId"`
	TaskID           string        `json:"taskId"`
	PointsAwarded    int           `json:"pointsAwarded"`
	CompletionDate   time.Time     `json:"completionDate"`
	VerificationData *string       `json:"verificationData,omitempty"`
	CreatedAt        time.Time     `json:"createdAt"`
	Task             *ActivityTask `json:"task,omitempty"`
}

type TaskCompletionHistoryInput struct {
	TaskID    *string    `json:"taskId,omitempty"`
	StartDate *time.Time `json:"startDate,omitempty"`
	EndDate   *time.Time `json:"endDate,omitempty"`
	Limit     *int       `json:"limit,omitempty"`
	Offset    *int       `json:"offset,omitempty"`
}

type TaskCompletionHistoryResponse struct {
	Success bool                     `json:"success"`
	Message string                   `json:"message"`
	Data    []*TaskCompletionHistory `json:"data"`
	Total   int                      `json:"total"`
}

type TaskCompletionResponse struct {
	Success       bool   `json:"success"`
	Message       string `json:"message"`
	PointsAwarded int    `json:"pointsAwarded"`
	NewTierLevel  *int   `json:"newTierLevel,omitempty"`
	TierUpgraded  bool   `json:"tierUpgraded"`
}

type TaskWithProgress struct {
	Task     *ActivityTask     `json:"task"`
	Progress *UserTaskProgress `json:"progress,omitempty"`
}

type TierBenefit struct {
	ID                  string    `json:"id"`
	TierLevel           int       `json:"tierLevel"`
	TierName            string    `json:"tierName"`
	MinPoints           int       `json:"minPoints"`
	CashbackPercentage  float64   `json:"cashbackPercentage"`
	BenefitsDescription *string   `json:"benefitsDescription,omitempty"`
	TierColor           *string   `json:"tierColor,omitempty"`
	TierIcon            *string   `json:"tierIcon,omitempty"`
	IsActive            bool      `json:"isActive"`
	CreatedAt           time.Time `json:"createdAt"`
	UpdatedAt           time.Time `json:"updatedAt"`
}

type TierBenefitResponse struct {
	Success bool         `json:"success"`
	Message string       `json:"message"`
	Data    *TierBenefit `json:"data,omitempty"`
}

type TierBenefitsResponse struct {
	Success bool           `json:"success"`
	Message string         `json:"message"`
	Data    []*TierBenefit `json:"data"`
}

type TransactionData struct {
	TransactionAmountUsd float64 `json:"transactionAmountUsd"`
	ClaimedUsd           float64 `json:"claimedUsd"`
	PendingClaimUsd      float64 `json:"pendingClaimUsd"`
	InvitationCount      int     `json:"invitationCount"`
	TransactingUserCount int     `json:"transactingUserCount"`
	ContractVolumeUsd    float64 `json:"contractVolumeUsd"`
	MemeVolumeUsd        float64 `json:"memeVolumeUsd"`
}

type TransactionDataInput struct {
	DataType  TransactionDataType `json:"dataType"`
	TimeRange TimeRangeType       `json:"timeRange"`
}

type TransactionDataResponse struct {
	TransactionData []*TransactionData `json:"transactionData"`
	Success         bool               `json:"success"`
	Message         *string            `json:"message,omitempty"`
}

type UpdateInfiniteAgentConfigInput struct {
	ID              string      `json:"id"`
	UserID          string      `json:"userID"`
	CommissionRateN *float64    `json:"commissionRateN,omitempty"`
	Status          *StatusType `json:"status,omitempty"`
}

type UpdateInfiniteAgentConfigResponse struct {
	InfiniteAgentConfig *InfiniteAgentConfig `json:"infiniteAgentConfig,omitempty"`
	Success             bool                 `json:"success"`
	Message             string               `json:"message"`
}

type UpdateLevelCommissionInput struct {
	LevelID                string  `json:"levelId"`
	DirectCommissionRate   float64 `json:"directCommissionRate"`
	IndirectCommissionRate float64 `json:"indirectCommissionRate"`
	ExtendedCommissionRate float64 `json:"extendedCommissionRate"`
	MemeFeeRebate          float64 `json:"memeFeeRebate"`
}

type UpdateLevelCommissionResponse struct {
	Level   *AgentLevel `json:"level,omitempty"`
	Success bool        `json:"success"`
	Message string      `json:"message"`
}

type UpdateTaskInput struct {
	ID                 string         `json:"id"`
	CategoryID         *string        `json:"categoryId,omitempty"`
	Name               *string        `json:"name,omitempty"`
	Description        *string        `json:"description,omitempty"`
	TaskType           *TaskType      `json:"taskType,omitempty"`
	Frequency          *TaskFrequency `json:"frequency,omitempty"`
	Points             *int           `json:"points,omitempty"`
	MaxCompletions     *int           `json:"maxCompletions,omitempty"`
	ResetPeriod        *string        `json:"resetPeriod,omitempty"`
	Conditions         *string        `json:"conditions,omitempty"`
	ActionTarget       *string        `json:"actionTarget,omitempty"`
	VerificationMethod *string        `json:"verificationMethod,omitempty"`
	ExternalLink       *string        `json:"externalLink,omitempty"`
	IsActive           *bool          `json:"isActive,omitempty"`
	StartDate          *time.Time     `json:"startDate,omitempty"`
	EndDate            *time.Time     `json:"endDate,omitempty"`
	SortOrder          *int           `json:"sortOrder,omitempty"`
}

type UpdateTierBenefitInput struct {
	ID                  string   `json:"id"`
	TierLevel           *int     `json:"tierLevel,omitempty"`
	TierName            *string  `json:"tierName,omitempty"`
	MinPoints           *int     `json:"minPoints,omitempty"`
	CashbackPercentage  *float64 `json:"cashbackPercentage,omitempty"`
	BenefitsDescription *string  `json:"benefitsDescription,omitempty"`
	TierColor           *string  `json:"tierColor,omitempty"`
	TierIcon            *string  `json:"tierIcon,omitempty"`
	IsActive            *bool    `json:"isActive,omitempty"`
}

type User struct {
	ID                        string            `json:"id"`
	Email                     *string           `json:"email,omitempty"`
	InvitationCode            *string           `json:"invitationCode,omitempty"`
	CreatedAt                 string            `json:"createdAt"`
	UpdatedAt                 string            `json:"updatedAt"`
	DeletedAt                 *string           `json:"deletedAt,omitempty"`
	AgentLevelID              int               `json:"agentLevelId"`
	AgentLevel                *AgentLevel       `json:"agentLevel"`
	LevelGracePeriodStartedAt *string           `json:"levelGracePeriodStartedAt,omitempty"`
	LevelUpgradedAt           *string           `json:"levelUpgradedAt,omitempty"`
	FirstTransactionAt        *string           `json:"firstTransactionAt,omitempty"`
	Referrals                 []*Referral       `json:"referrals"`
	ReferralSnapshot          *ReferralSnapshot `json:"referralSnapshot,omitempty"`
	ReferredUsers             []*Referral       `json:"referredUsers"`
}

type UserDashboard struct {
	UserTierInfo      *UserTierInfo            `json:"userTierInfo"`
	TierBenefit       *TierBenefit             `json:"tierBenefit"`
	NextTier          *TierBenefit             `json:"nextTier,omitempty"`
	PointsToNextTier  int                      `json:"pointsToNextTier"`
	ClaimableCashback float64                  `json:"claimableCashback"`
	RecentClaims      []*ActivityCashbackClaim `json:"recentClaims"`
	UserRank          int                      `json:"userRank"`
}

type UserDashboardResponse struct {
	Success bool           `json:"success"`
	Message string         `json:"message"`
	Data    *UserDashboard `json:"data,omitempty"`
}

type UserLevelInfo struct {
	CurrentLevel   *AgentLevel `json:"currentLevel"`
	MemeVolume     float64     `json:"memeVolume"`
	ContractVolume float64     `json:"contractVolume"`
	TotalVolume    float64     `json:"totalVolume"`
}

type UserLevelInfoResponse struct {
	Success bool           `json:"success"`
	Message string         `json:"message"`
	Data    *UserLevelInfo `json:"data,omitempty"`
}

type UserTaskProgress struct {
	ID                 string        `json:"id"`
	UserID             string        `json:"userId"`
	TaskID             string        `json:"taskId"`
	Status             TaskStatus    `json:"status"`
	ProgressValue      int           `json:"progressValue"`
	TargetValue        *int          `json:"targetValue,omitempty"`
	CompletionCount    int           `json:"completionCount"`
	PointsEarned       int           `json:"pointsEarned"`
	LastCompletedAt    *time.Time    `json:"lastCompletedAt,omitempty"`
	LastResetAt        *time.Time    `json:"lastResetAt,omitempty"`
	StreakCount        int           `json:"streakCount"`
	Metadata           *string       `json:"metadata,omitempty"`
	CreatedAt          time.Time     `json:"createdAt"`
	UpdatedAt          time.Time     `json:"updatedAt"`
	Task               *ActivityTask `json:"task,omitempty"`
	ProgressPercentage float64       `json:"progressPercentage"`
	CanBeClaimed       bool          `json:"canBeClaimed"`
}

type UserTaskProgressResponse struct {
	Success bool                `json:"success"`
	Message string              `json:"message"`
	Data    []*UserTaskProgress `json:"data"`
}

type UserTierInfo struct {
	UserID                string       `json:"userId"`
	CurrentTier           int          `json:"currentTier"`
	TotalPoints           int          `json:"totalPoints"`
	PointsThisMonth       int          `json:"pointsThisMonth"`
	TradingVolumeUsd      float64      `json:"tradingVolumeUsd"`
	ActiveDaysThisMonth   int          `json:"activeDaysThisMonth"`
	CumulativeCashbackUsd float64      `json:"cumulativeCashbackUsd"`
	ClaimableCashbackUsd  float64      `json:"claimableCashbackUsd"`
	ClaimedCashbackUsd    float64      `json:"claimedCashbackUsd"`
	LastActivityDate      *time.Time   `json:"lastActivityDate,omitempty"`
	TierUpgradedAt        *time.Time   `json:"tierUpgradedAt,omitempty"`
	MonthlyResetAt        *time.Time   `json:"monthlyResetAt,omitempty"`
	CreatedAt             time.Time    `json:"createdAt"`
	UpdatedAt             time.Time    `json:"updatedAt"`
	TierBenefit           *TierBenefit `json:"tierBenefit,omitempty"`
	UserRank              *int         `json:"userRank,omitempty"`
}

type WithdrawalRecord struct {
	Hash             string `json:"hash"`
	WithdrawalReward string `json:"withdrawalReward"`
	Date             string `json:"date"`
}

type WithdrawalRecordRequest struct {
	Page     int `json:"page"`
	PageSize int `json:"pageSize"`
}

type WithdrawalRecordResponse struct {
	Data     []*WithdrawalRecord `json:"data"`
	Total    int                 `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"pageSize"`
	Success  bool                `json:"success"`
	Message  *string             `json:"message,omitempty"`
}

type ClaimStatus string

const (
	ClaimStatusPending    ClaimStatus = "PENDING"
	ClaimStatusProcessing ClaimStatus = "PROCESSING"
	ClaimStatusCompleted  ClaimStatus = "COMPLETED"
	ClaimStatusFailed     ClaimStatus = "FAILED"
)

var AllClaimStatus = []ClaimStatus{
	ClaimStatusPending,
	ClaimStatusProcessing,
	ClaimStatusCompleted,
	ClaimStatusFailed,
}

func (e ClaimStatus) IsValid() bool {
	switch e {
	case ClaimStatusPending, ClaimStatusProcessing, ClaimStatusCompleted, ClaimStatusFailed:
		return true
	}
	return false
}

func (e ClaimStatus) String() string {
	return string(e)
}

func (e *ClaimStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ClaimStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ClaimStatus", str)
	}
	return nil
}

func (e ClaimStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *ClaimStatus) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e ClaimStatus) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type ClaimType string

const (
	ClaimTypeTradingCashback ClaimType = "TRADING_CASHBACK"
	ClaimTypeTaskReward      ClaimType = "TASK_REWARD"
	ClaimTypeTierBonus       ClaimType = "TIER_BONUS"
	ClaimTypeReferralBonus   ClaimType = "REFERRAL_BONUS"
)

var AllClaimType = []ClaimType{
	ClaimTypeTradingCashback,
	ClaimTypeTaskReward,
	ClaimTypeTierBonus,
	ClaimTypeReferralBonus,
}

func (e ClaimType) IsValid() bool {
	switch e {
	case ClaimTypeTradingCashback, ClaimTypeTaskReward, ClaimTypeTierBonus, ClaimTypeReferralBonus:
		return true
	}
	return false
}

func (e ClaimType) String() string {
	return string(e)
}

func (e *ClaimType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ClaimType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ClaimType", str)
	}
	return nil
}

func (e ClaimType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *ClaimType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e ClaimType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type DataOverviewTimeRange string

const (
	DataOverviewTimeRangeToday      DataOverviewTimeRange = "TODAY"
	DataOverviewTimeRangeLast30Days DataOverviewTimeRange = "LAST_30_DAYS"
	DataOverviewTimeRangeLast60Days DataOverviewTimeRange = "LAST_60_DAYS"
	DataOverviewTimeRangeAllTime    DataOverviewTimeRange = "ALL_TIME"
)

var AllDataOverviewTimeRange = []DataOverviewTimeRange{
	DataOverviewTimeRangeToday,
	DataOverviewTimeRangeLast30Days,
	DataOverviewTimeRangeLast60Days,
	DataOverviewTimeRangeAllTime,
}

func (e DataOverviewTimeRange) IsValid() bool {
	switch e {
	case DataOverviewTimeRangeToday, DataOverviewTimeRangeLast30Days, DataOverviewTimeRangeLast60Days, DataOverviewTimeRangeAllTime:
		return true
	}
	return false
}

func (e DataOverviewTimeRange) String() string {
	return string(e)
}

func (e *DataOverviewTimeRange) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = DataOverviewTimeRange(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid DataOverviewTimeRange", str)
	}
	return nil
}

func (e DataOverviewTimeRange) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *DataOverviewTimeRange) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e DataOverviewTimeRange) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type DataOverviewType string

const (
	DataOverviewTypeAll      DataOverviewType = "ALL"
	DataOverviewTypeMeme     DataOverviewType = "MEME"
	DataOverviewTypeContract DataOverviewType = "CONTRACT"
)

var AllDataOverviewType = []DataOverviewType{
	DataOverviewTypeAll,
	DataOverviewTypeMeme,
	DataOverviewTypeContract,
}

func (e DataOverviewType) IsValid() bool {
	switch e {
	case DataOverviewTypeAll, DataOverviewTypeMeme, DataOverviewTypeContract:
		return true
	}
	return false
}

func (e DataOverviewType) String() string {
	return string(e)
}

func (e *DataOverviewType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = DataOverviewType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid DataOverviewType", str)
	}
	return nil
}

func (e DataOverviewType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *DataOverviewType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e DataOverviewType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type StatusType string

const (
	StatusTypeActive   StatusType = "ACTIVE"
	StatusTypeInactive StatusType = "INACTIVE"
)

var AllStatusType = []StatusType{
	StatusTypeActive,
	StatusTypeInactive,
}

func (e StatusType) IsValid() bool {
	switch e {
	case StatusTypeActive, StatusTypeInactive:
		return true
	}
	return false
}

func (e StatusType) String() string {
	return string(e)
}

func (e *StatusType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = StatusType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid StatusType", str)
	}
	return nil
}

func (e StatusType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *StatusType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e StatusType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskFrequency string

const (
	TaskFrequencyDaily       TaskFrequency = "DAILY"
	TaskFrequencyOneTime     TaskFrequency = "ONE_TIME"
	TaskFrequencyUnlimited   TaskFrequency = "UNLIMITED"
	TaskFrequencyProgressive TaskFrequency = "PROGRESSIVE"
	TaskFrequencyManual      TaskFrequency = "MANUAL"
)

var AllTaskFrequency = []TaskFrequency{
	TaskFrequencyDaily,
	TaskFrequencyOneTime,
	TaskFrequencyUnlimited,
	TaskFrequencyProgressive,
	TaskFrequencyManual,
}

func (e TaskFrequency) IsValid() bool {
	switch e {
	case TaskFrequencyDaily, TaskFrequencyOneTime, TaskFrequencyUnlimited, TaskFrequencyProgressive, TaskFrequencyManual:
		return true
	}
	return false
}

func (e TaskFrequency) String() string {
	return string(e)
}

func (e *TaskFrequency) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskFrequency(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskFrequency", str)
	}
	return nil
}

func (e TaskFrequency) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskFrequency) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskFrequency) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskIdentifier string

const (
	TaskIdentifierDailyCheckin           TaskIdentifier = "DAILY_CHECKIN"
	TaskIdentifierMemeTradeDaily         TaskIdentifier = "MEME_TRADE_DAILY"
	TaskIdentifierPerpetualTradeDaily    TaskIdentifier = "PERPETUAL_TRADE_DAILY"
	TaskIdentifierMarketPageView         TaskIdentifier = "MARKET_PAGE_VIEW"
	TaskIdentifierConsecutiveCheckin     TaskIdentifier = "CONSECUTIVE_CHECKIN"
	TaskIdentifierConsecutiveTradingDays TaskIdentifier = "CONSECUTIVE_TRADING_DAYS"
	TaskIdentifierTwitterFollow          TaskIdentifier = "TWITTER_FOLLOW"
	TaskIdentifierTwitterRetweet         TaskIdentifier = "TWITTER_RETWEET"
	TaskIdentifierTwitterLike            TaskIdentifier = "TWITTER_LIKE"
	TaskIdentifierTelegramJoin           TaskIdentifier = "TELEGRAM_JOIN"
	TaskIdentifierInviteFriends          TaskIdentifier = "INVITE_FRIENDS"
	TaskIdentifierShareReferral          TaskIdentifier = "SHARE_REFERRAL"
	TaskIdentifierTradingPoints          TaskIdentifier = "TRADING_POINTS"
	TaskIdentifierAccumulatedTrading10k  TaskIdentifier = "ACCUMULATED_TRADING_10K"
	TaskIdentifierAccumulatedTrading50k  TaskIdentifier = "ACCUMULATED_TRADING_50K"
	TaskIdentifierAccumulatedTrading100k TaskIdentifier = "ACCUMULATED_TRADING_100K"
	TaskIdentifierAccumulatedTrading500k TaskIdentifier = "ACCUMULATED_TRADING_500K"
)

var AllTaskIdentifier = []TaskIdentifier{
	TaskIdentifierDailyCheckin,
	TaskIdentifierMemeTradeDaily,
	TaskIdentifierPerpetualTradeDaily,
	TaskIdentifierMarketPageView,
	TaskIdentifierConsecutiveCheckin,
	TaskIdentifierConsecutiveTradingDays,
	TaskIdentifierTwitterFollow,
	TaskIdentifierTwitterRetweet,
	TaskIdentifierTwitterLike,
	TaskIdentifierTelegramJoin,
	TaskIdentifierInviteFriends,
	TaskIdentifierShareReferral,
	TaskIdentifierTradingPoints,
	TaskIdentifierAccumulatedTrading10k,
	TaskIdentifierAccumulatedTrading50k,
	TaskIdentifierAccumulatedTrading100k,
	TaskIdentifierAccumulatedTrading500k,
}

func (e TaskIdentifier) IsValid() bool {
	switch e {
	case TaskIdentifierDailyCheckin, TaskIdentifierMemeTradeDaily, TaskIdentifierPerpetualTradeDaily, TaskIdentifierMarketPageView, TaskIdentifierConsecutiveCheckin, TaskIdentifierConsecutiveTradingDays, TaskIdentifierTwitterFollow, TaskIdentifierTwitterRetweet, TaskIdentifierTwitterLike, TaskIdentifierTelegramJoin, TaskIdentifierInviteFriends, TaskIdentifierShareReferral, TaskIdentifierTradingPoints, TaskIdentifierAccumulatedTrading10k, TaskIdentifierAccumulatedTrading50k, TaskIdentifierAccumulatedTrading100k, TaskIdentifierAccumulatedTrading500k:
		return true
	}
	return false
}

func (e TaskIdentifier) String() string {
	return string(e)
}

func (e *TaskIdentifier) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskIdentifier(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskIdentifier", str)
	}
	return nil
}

func (e TaskIdentifier) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskIdentifier) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskIdentifier) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskStatus string

const (
	TaskStatusNotStarted TaskStatus = "NOT_STARTED"
	TaskStatusInProgress TaskStatus = "IN_PROGRESS"
	TaskStatusCompleted  TaskStatus = "COMPLETED"
	TaskStatusClaimed    TaskStatus = "CLAIMED"
	TaskStatusExpired    TaskStatus = "EXPIRED"
)

var AllTaskStatus = []TaskStatus{
	TaskStatusNotStarted,
	TaskStatusInProgress,
	TaskStatusCompleted,
	TaskStatusClaimed,
	TaskStatusExpired,
}

func (e TaskStatus) IsValid() bool {
	switch e {
	case TaskStatusNotStarted, TaskStatusInProgress, TaskStatusCompleted, TaskStatusClaimed, TaskStatusExpired:
		return true
	}
	return false
}

func (e TaskStatus) String() string {
	return string(e)
}

func (e *TaskStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskStatus", str)
	}
	return nil
}

func (e TaskStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskStatus) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskStatus) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskType string

const (
	TaskTypeDaily        TaskType = "DAILY"
	TaskTypeOneTime      TaskType = "ONE_TIME"
	TaskTypeUnlimited    TaskType = "UNLIMITED"
	TaskTypeProgressive  TaskType = "PROGRESSIVE"
	TaskTypeManualUpdate TaskType = "MANUAL_UPDATE"
)

var AllTaskType = []TaskType{
	TaskTypeDaily,
	TaskTypeOneTime,
	TaskTypeUnlimited,
	TaskTypeProgressive,
	TaskTypeManualUpdate,
}

func (e TaskType) IsValid() bool {
	switch e {
	case TaskTypeDaily, TaskTypeOneTime, TaskTypeUnlimited, TaskTypeProgressive, TaskTypeManualUpdate:
		return true
	}
	return false
}

func (e TaskType) String() string {
	return string(e)
}

func (e *TaskType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskType", str)
	}
	return nil
}

func (e TaskType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TimeRangeType string

const (
	TimeRangeTypeToday      TimeRangeType = "TODAY"
	TimeRangeTypeLast30Days TimeRangeType = "LAST_30_DAYS"
	TimeRangeTypeLast60Days TimeRangeType = "LAST_60_DAYS"
	TimeRangeTypeAllTime    TimeRangeType = "ALL_TIME"
)

var AllTimeRangeType = []TimeRangeType{
	TimeRangeTypeToday,
	TimeRangeTypeLast30Days,
	TimeRangeTypeLast60Days,
	TimeRangeTypeAllTime,
}

func (e TimeRangeType) IsValid() bool {
	switch e {
	case TimeRangeTypeToday, TimeRangeTypeLast30Days, TimeRangeTypeLast60Days, TimeRangeTypeAllTime:
		return true
	}
	return false
}

func (e TimeRangeType) String() string {
	return string(e)
}

func (e *TimeRangeType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TimeRangeType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TimeRangeType", str)
	}
	return nil
}

func (e TimeRangeType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TimeRangeType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TimeRangeType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TransactionDataType string

const (
	TransactionDataTypeAll      TransactionDataType = "ALL"
	TransactionDataTypeMeme     TransactionDataType = "MEME"
	TransactionDataTypeContract TransactionDataType = "CONTRACT"
)

var AllTransactionDataType = []TransactionDataType{
	TransactionDataTypeAll,
	TransactionDataTypeMeme,
	TransactionDataTypeContract,
}

func (e TransactionDataType) IsValid() bool {
	switch e {
	case TransactionDataTypeAll, TransactionDataTypeMeme, TransactionDataTypeContract:
		return true
	}
	return false
}

func (e TransactionDataType) String() string {
	return string(e)
}

func (e *TransactionDataType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TransactionDataType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TransactionDataType", str)
	}
	return nil
}

func (e TransactionDataType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TransactionDataType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TransactionDataType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TransactionType string

const (
	TransactionTypeAll      TransactionType = "ALL"
	TransactionTypeMeme     TransactionType = "MEME"
	TransactionTypeContract TransactionType = "CONTRACT"
	TransactionTypeSpot     TransactionType = "SPOT"
)

var AllTransactionType = []TransactionType{
	TransactionTypeAll,
	TransactionTypeMeme,
	TransactionTypeContract,
	TransactionTypeSpot,
}

func (e TransactionType) IsValid() bool {
	switch e {
	case TransactionTypeAll, TransactionTypeMeme, TransactionTypeContract, TransactionTypeSpot:
		return true
	}
	return false
}

func (e TransactionType) String() string {
	return string(e)
}

func (e *TransactionType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TransactionType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TransactionType", str)
	}
	return nil
}

func (e TransactionType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TransactionType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TransactionType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type WalletType string

const (
	WalletTypeEmbedded WalletType = "EMBEDDED"
	WalletTypeManaged  WalletType = "MANAGED"
)

var AllWalletType = []WalletType{
	WalletTypeEmbedded,
	WalletTypeManaged,
}

func (e WalletType) IsValid() bool {
	switch e {
	case WalletTypeEmbedded, WalletTypeManaged:
		return true
	}
	return false
}

func (e WalletType) String() string {
	return string(e)
}

func (e *WalletType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = WalletType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid WalletType", str)
	}
	return nil
}

func (e WalletType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *WalletType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e WalletType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type WithdrawalStatus string

const (
	WithdrawalStatusPending   WithdrawalStatus = "PENDING"
	WithdrawalStatusCompleted WithdrawalStatus = "COMPLETED"
	WithdrawalStatusFailed    WithdrawalStatus = "FAILED"
	WithdrawalStatusCancelled WithdrawalStatus = "CANCELLED"
)

var AllWithdrawalStatus = []WithdrawalStatus{
	WithdrawalStatusPending,
	WithdrawalStatusCompleted,
	WithdrawalStatusFailed,
	WithdrawalStatusCancelled,
}

func (e WithdrawalStatus) IsValid() bool {
	switch e {
	case WithdrawalStatusPending, WithdrawalStatusCompleted, WithdrawalStatusFailed, WithdrawalStatusCancelled:
		return true
	}
	return false
}

func (e WithdrawalStatus) String() string {
	return string(e)
}

func (e *WithdrawalStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = WithdrawalStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid WithdrawalStatus", str)
	}
	return nil
}

func (e WithdrawalStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *WithdrawalStatus) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e WithdrawalStatus) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
